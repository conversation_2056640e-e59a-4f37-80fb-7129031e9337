#!/bin/bash
# 微信Bot Webhook调试程序启动脚本

echo "微信Bot Webhook调试程序"
echo "======================="

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python 3"
    exit 1
fi

# 检查Flask是否安装
if ! python3 -c "import flask" 2>/dev/null; then
    echo "警告: 未安装Flask，正在安装..."
    pip3 install flask
fi

# 显示使用说明
echo ""
echo "使用方法:"
echo "1. 监听所有wxid的消息:"
echo "   python3 debug_webhook.py"
echo ""
echo "2. 监听指定wxid的消息:"
echo "   python3 debug_webhook.py --wxid your_wxid"
echo ""
echo "3. 自定义端口:"
echo "   python3 debug_webhook.py --port 9000"
echo ""
echo "4. 启用调试模式:"
echo "   python3 debug_webhook.py --debug"
echo ""

# 如果提供了参数，直接启动
if [ $# -gt 0 ]; then
    echo "启动调试程序..."
    python3 debug_webhook.py "$@"
else
    echo "请选择启动方式，或直接运行: python3 debug_webhook.py [参数]"
fi
