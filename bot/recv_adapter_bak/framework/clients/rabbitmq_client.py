"""
RabbitMQ客户端
"""
import asyncio
import json
from typing import Callable, Dict, Any
import aio_pika
from aio_pika import connect_robust, Message
from loguru import logger

from config import settings


class RabbitMQClient:
    """RabbitMQ客户端"""
    
    def __init__(self):
        self.connection = None
        self.channel = None
        self.consumers = {}
        
    async def connect(self):
        """连接到RabbitMQ"""
        try:
            connection_url = f"amqp://{settings.rabbitmq_username}:{settings.rabbitmq_password}@{settings.rabbitmq_host}:{settings.rabbitmq_port}{settings.rabbitmq_vhost}"
            self.connection = await connect_robust(connection_url)
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=1)
            logger.info("Connected to RabbitMQ")
        except Exception as e:
            logger.error(f"Failed to connect to RabbitMQ: {e}")
            raise
    
    async def disconnect(self):
        """断开连接"""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from RabbitM<PERSON>")
    
    async def start_consuming(self, queue_name: str, callback: Callable, version: int):
        """开始消费指定队列"""
        try:
            # 声明队列 - 使用passive=True来避免参数冲突
            # 如果队列已存在，就直接使用现有队列的配置
            queue = await self.channel.declare_queue(queue_name, passive=True)
            
            # 创建消费者
            async def message_handler(message: aio_pika.IncomingMessage):
                async with message.process():
                    try:
                        # 解析JSON消息
                        body = message.body.decode('utf-8')
                        data = json.loads(body)
                        
                        # 调用回调函数处理消息
                        await callback(data, queue_name, version)
                        
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON from queue {queue_name}: {e}")
                    except Exception as e:
                        logger.error(f"Error processing message from queue {queue_name}: {e}")
            
            # 开始消费
            consumer_tag = await queue.consume(message_handler)
            self.consumers[queue_name] = consumer_tag
            
            logger.info(f"Started consuming from queue: {queue_name} (version {version})")
            
        except Exception as e:
            logger.error(f"Failed to start consuming from queue {queue_name}: {e}")
            raise
    
    async def stop_consuming(self, queue_name: str):
        """停止消费指定队列"""
        if queue_name in self.consumers:
            await self.channel.basic_cancel(self.consumers[queue_name])
            del self.consumers[queue_name]
            logger.info(f"Stopped consuming from queue: {queue_name}")
    
    async def start_all_consumers(self, message_callback: Callable):
        """启动所有队列的消费者"""
        tasks = []
        for queue_name, version in settings.queues.items():
            task = asyncio.create_task(
                self.start_consuming(queue_name, message_callback, version)
            )
            tasks.append(task)
        
        # 等待所有消费者启动
        await asyncio.gather(*tasks)
        logger.info("All consumers started")
    
    async def stop_all_consumers(self):
        """停止所有消费者"""
        for queue_name in list(self.consumers.keys()):
            await self.stop_consuming(queue_name)
        logger.info("All consumers stopped")
