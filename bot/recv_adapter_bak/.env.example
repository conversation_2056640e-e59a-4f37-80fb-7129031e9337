# RabbitMQ连接配置
RABBITMQ_HOST=**************
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=your_username
RABBITMQ_PASSWORD=your_password
RABBITMQ_VHOST=/

# RabbitMQ管理API配置
RABBITMQ_MANAGEMENT_HOST=**************
RABBITMQ_MANAGEMENT_PORT=15672
RABBITMQ_MANAGEMENT_USERNAME=your_management_username
RABBITMQ_MANAGEMENT_PASSWORD=your_management_password

# HTTP转发配置
FORWARD_BASE_URL=http://**************:9312/recv

# 消息过滤配置
FILTER_HISTORY_MINUTES=1

# 去重配置
DEDUP_HISTORY_SIZE=10000

# 定时清理配置
CLEANUP_INTERVAL_MINUTES=1
CLEANUP_QUEUE_PREFIX=wx_messages

# Webhook配置
WEBHOOK_ENABLED=true
WEBHOOK_HOST=0.0.0.0
WEBHOOK_PORT=8080
WEBHOOK_PATH=/webhook

# 日志配置
LOG_LEVEL=INFO
