# 消息转发器使用说明

## 概述

这是一个临时的消息转发器，用于将OpenIM和群公告消息转发到指定的HTTP接口。

## 功能特性

1. **OpenIM消息转发**: 自动识别并转发来自OpenIM的消息
2. **群公告消息转发**: 自动识别并转发群公告消息
3. **消息格式转换**: 将StandardMessage格式转换为目标API所需的格式
4. **异步处理**: 使用异步HTTP客户端进行消息转发

## 目标接口

- **URL**: `http://cloud.yaoboan.com:253/api/receive_message`
- **方法**: POST
- **内容类型**: application/json

## 消息格式

转发的消息将被转换为以下格式：

```json
{
    "TypeName": "AddMsg",
    "Appid": "wx_K0G1TkCSGAjEd1nLdPr4n",
    "Wxid": "wxid_phyyedw9xap22",
    "Data": {
        "MsgId": 随机生成的消息ID,
        "FromUserName": {
            "string": "原始群ID或发送者ID"
        },
        "ToUserName": {
            "string": "wxid_0xsqb3o0tsvz22"
        },
        "MsgType": 1,
        "Content": {
            "string": "原始消息内容"
        },
        "Status": 3,
        "ImgStatus": 1,
        "ImgBuf": {
            "iLen": 0
        },
        "CreateTime": 消息发送时间戳
    }
}
```

## 消息识别规则

### OpenIM消息
- 发送者ID以 `@openim` 结尾的消息

### 群公告消息
- 消息类型为应用消息(49)且内容包含"群公告"或"群通知"
- 消息源字段包含"群公告"
- 内容包含以下关键词之一：
  - "群公告"
  - "群通知"
  - "管理员发布"
  - "群主发布"

## 代码结构

### MessageForwarder类
位置：`handlers/text_handler.py`

主要方法：
- `convert_message_format()`: 消息格式转换
- `forward_message()`: 发送HTTP请求转发消息
- `start()/stop()`: 启动/停止转发器

### TextMessageHandler更新
添加了以下方法：
- `is_group_announcement()`: 判断是否为群公告消息
- `handle_group_announcement()`: 处理群公告消息
- `handle_openim_message()`: 处理OpenIM消息（已更新为转发消息）

## 使用方法

### 1. 启动消息处理器

```python
from handlers.text_handler import TextMessageHandler

handler = TextMessageHandler()
await handler.start()  # 启动转发器

# 处理消息
result = await handler.handle_text_message(message)

await handler.stop()   # 停止转发器
```

### 2. 直接使用转发器

```python
from handlers.text_handler import MessageForwarder

forwarder = MessageForwarder()
await forwarder.start()

# 转发消息
result = await forwarder.forward_message(message)

await forwarder.stop()
```

## 测试

运行测试脚本：

```bash
cd bot/recv_adapter
python test_message_forwarder.py
```

测试包括：
1. 消息格式转换测试
2. HTTP转发功能测试（可选）
3. 消息识别逻辑测试

## 配置

### 修改目标URL
在 `MessageForwarder` 类的构造函数中修改：

```python
def __init__(self, target_url: str = "你的目标URL"):
    self.target_url = target_url
```

### 修改固定参数
在 `convert_message_format()` 方法中修改：

```python
converted_message = {
    "TypeName": "AddMsg",
    "Appid": "你的Appid",
    "Wxid": "你的Wxid",
    "Data": {
        # ...
        "ToUserName": {
            "string": "你的目标用户ID"
        },
        # ...
    }
}
```

## 日志

转发器会记录以下日志：
- 成功转发的消息
- 转发失败的消息及错误信息
- OpenIM和群公告消息的识别

日志级别：
- INFO: 成功转发
- ERROR: 转发失败
- DEBUG: 详细调试信息

## 注意事项

1. **网络连接**: 确保能够访问目标HTTP接口
2. **异步处理**: 所有方法都是异步的，需要使用await调用
3. **错误处理**: 转发失败不会影响其他消息的处理
4. **资源管理**: 记得调用start()和stop()方法管理HTTP会话

## 故障排除

### 转发失败
1. 检查网络连接
2. 检查目标URL是否正确
3. 查看日志中的错误信息

### 消息未被识别
1. 检查消息类型和内容
2. 查看识别规则是否匹配
3. 添加调试日志确认消息字段

### HTTP超时
1. 检查网络延迟
2. 考虑增加超时时间
3. 检查目标服务器状态

## 扩展功能

如需添加更多消息类型的转发，可以：

1. 在 `TextMessageHandler` 中添加新的识别方法
2. 在 `handle_group_text()` 中添加新的处理分支
3. 根据需要修改消息格式转换逻辑

## 临时性说明

这是一个临时的解决方案，主要用于快速实现消息转发功能。在生产环境中使用时，建议：

1. 添加更完善的错误处理和重试机制
2. 添加配置文件支持
3. 添加更详细的日志和监控
4. 考虑消息队列等更可靠的转发方式
