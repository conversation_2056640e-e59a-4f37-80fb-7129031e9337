"""
群组消息处理器
"""
import re
from typing import List, Optional
from loguru import logger

from .models import StandardMessage


class GroupProcessor:
    """群组消息处理器"""
    
    def process_group_message(self, message: StandardMessage) -> StandardMessage:
        """处理群组消息"""
        # 检查是否是群组消息（以@chatroom结尾）
        if not message.from_user_name.endswith("@chatroom"):
            return message
        
        # 复制消息对象以避免修改原始对象
        processed_message = message.model_copy()
        
        # 设置群组ID
        processed_message.group_id = message.from_user_name
        
        # 解析群组消息内容格式：发送者:消息内容
        content_match = re.match(r'^([^:]+):([\s\S]*)', message.content)
        if content_match:
            processed_message.from_user_name = content_match.group(1)
            processed_message.content = content_match.group(2).strip()
        else:
            # 如果没有匹配到，保持原始内容
            processed_message.from_user_name = ""
        
        # 处理@列表
        processed_message.at_list = self._extract_at_list(message.msg_source)
        
        # 提取成员数量
        processed_message.member_count = self._extract_member_count(message.msg_source)
        
        logger.debug(f"Processed group message: group_id={processed_message.group_id}, "
                    f"from={processed_message.from_user_name}, "
                    f"at_list={processed_message.at_list}, "
                    f"member_count={processed_message.member_count}")
        
        return processed_message
    
    def _extract_at_list(self, msg_source: str) -> Optional[List[str]]:
        """从msg_source中提取@列表"""
        if not msg_source or "<atuserlist>" not in msg_source:
            return None
        
        # 提取CDATA内容
        at_match = re.search(r'<atuserlist><!\[CDATA\[([^\]]+)\]\]></atuserlist>', msg_source)
        if not at_match:
            return None
        
        # 按逗号分割并去除空格
        at_ids = [id_str.strip() for id_str in at_match.group(1).split(',') if id_str.strip()]
        
        return at_ids if at_ids else None
    
    def _extract_member_count(self, msg_source: str) -> Optional[int]:
        """从msg_source中提取成员数量"""
        if not msg_source:
            return None
        
        member_count_match = re.search(r'<membercount>(\d+)</membercount>', msg_source)
        if member_count_match:
            try:
                return int(member_count_match.group(1))
            except ValueError:
                logger.warning(f"Invalid member count format: {member_count_match.group(1)}")
                return None
        
        return None
