"""
Webhook接收器
"""
import asyncio
import json
from typing import Callable, Dict, Any, Union
from aiohttp import web, ClientSession
from loguru import logger

from config import settings
from ..core.models import MessageVersion


class WebhookReceiver:
    """Webhook消息接收器"""
    
    def __init__(self):
        self.app = None
        self.runner = None
        self.site = None
        self.message_callback = None
        
    async def start(self, message_callback: Callable):
        """启动webhook服务器"""
        self.message_callback = message_callback
        
        # 创建aiohttp应用
        self.app = web.Application()
        
        # 添加路由
        self.app.router.add_post(settings.webhook_path, self.handle_webhook)
        self.app.router.add_get('/health', self.health_check)
        
        # 启动服务器
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        
        self.site = web.TCPSite(
            self.runner, 
            settings.webhook_host, 
            settings.webhook_port
        )
        await self.site.start()
        
        logger.info(f"Webhook server started on {settings.webhook_host}:{settings.webhook_port}{settings.webhook_path}")
    
    async def stop(self):
        """停止webhook服务器"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()
        logger.info("Webhook server stopped")
    
    async def health_check(self, request):
        """健康检查接口"""
        return web.json_response({
            "status": "ok",
            "service": "wechat-bot-webhook-receiver",
            "version": "3.0"
        })
    
    async def handle_webhook(self, request):
        """处理webhook请求"""
        try:
            # 获取请求数据
            data = await request.json()

            logger.debug(f"Received webhook data: {json.dumps(data, ensure_ascii=False)}")

            # 基本验证
            if not self._validate_webhook_data(data):
                return web.json_response(
                    {"status": "error", "message": "Invalid webhook data"},
                    status=400
                )

            # 统一通过消息处理回调处理所有类型的webhook消息
            if self.message_callback:
                await self.message_callback(data, "webhook", MessageVersion.V3)

            return web.json_response({"status": "success"})

        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in webhook request: {e}")
            return web.json_response(
                {"status": "error", "message": "Invalid JSON"},
                status=400
            )
        except Exception as e:
            logger.error(f"Error handling webhook: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")
            return web.json_response(
                {"status": "error", "message": str(e)},
                status=500
            )

    def _validate_webhook_data(self, data: Dict[str, Any]) -> bool:
        """验证webhook数据格式"""
        # 检查必需字段
        if "TypeName" not in data:
            logger.error("Missing TypeName in webhook data")
            return False

        if "Appid" not in data:
            logger.error("Missing Appid in webhook data")
            return False

        if "Wxid" not in data:
            logger.error("Missing Wxid in webhook data")
            return False

        type_name = data["TypeName"]

        # 根据消息类型验证Data字段
        if type_name in ["AddMsg", "ModContacts", "DelContacts"]:
            if "Data" not in data or data["Data"] is None:
                logger.error(f"Missing Data field for {type_name} message")
                return False
        elif type_name == "Offline":
            # Offline消息不需要Data字段
            pass
        else:
            logger.warning(f"Unknown webhook message type: {type_name}")
            # 未知类型也允许通过，让后续处理决定

        return True
    



# 全局webhook接收器实例
webhook_receiver = WebhookReceiver()
