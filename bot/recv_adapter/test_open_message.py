#!/usr/bin/env python3
"""
测试"打开"消息（类型51）的处理功能
"""
import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from framework.core.models import StandardMessage, MessageType
from handlers.message_router import MessageRouter
from handlers.message_handler import MessageHandler


async def test_open_message_handler():
    """测试MessageHandler对打开消息的处理"""
    print("=== 测试MessageHandler处理打开消息 ===")
    
    handler = MessageHandler()
    
    # 创建打开消息（私聊）
    open_message_private = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123456,
        msg_type=MessageType.打开,  # 消息类型51
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_sender_user",  # 这是对方的wxid
        to_user_name="wxid_bot123",
        content="打开了聊天窗口",
        push_content="",
        msg_source="",
        ver=3
    )
    
    print("测试私聊打开消息...")
    result = await handler.handle_open_message(open_message_private)
    print(f"私聊打开消息处理结果: {result}\n")
    
    # 创建打开消息（群聊）
    open_message_group = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123457,
        msg_type=MessageType.打开,  # 消息类型51
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_group_member",  # 这是对方的wxid
        to_user_name="wxid_bot123",
        content="打开了群聊窗口",
        push_content="",
        msg_source="",
        ver=3
    )
    open_message_group.group_id = "12345@chatroom"
    
    print("测试群聊打开消息...")
    result = await handler.handle_open_message(open_message_group)
    print(f"群聊打开消息处理结果: {result}\n")


async def test_open_message_router():
    """测试MessageRouter对打开消息的路由"""
    print("=== 测试MessageRouter路由打开消息 ===")
    
    router = MessageRouter()
    
    # 创建打开消息
    open_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123458,
        msg_type=MessageType.打开,  # 消息类型51
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_test_sender",  # 这是对方的wxid
        to_user_name="wxid_bot123",
        content="通过路由器处理的打开消息",
        push_content="",
        msg_source="",
        ver=3
    )
    
    print("测试通过路由器处理打开消息...")
    result = await router.route_message(open_message)
    print(f"路由器处理结果: {result}\n")


async def test_different_open_scenarios():
    """测试不同场景的打开消息"""
    print("=== 测试不同场景的打开消息 ===")
    
    handler = MessageHandler()
    
    # 场景1：好友打开聊天
    friend_open = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123459,
        msg_type=MessageType.打开,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_friend_001",
        to_user_name="wxid_bot123",
        content="",
        push_content="",
        msg_source="",
        ver=3
    )
    
    print("场景1：好友打开聊天")
    await handler.handle_open_message(friend_open)
    print()
    
    # 场景2：陌生人打开聊天
    stranger_open = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123460,
        msg_type=MessageType.打开,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_stranger_002",
        to_user_name="wxid_bot123",
        content="陌生人打开聊天",
        push_content="",
        msg_source="stranger",
        ver=3
    )
    
    print("场景2：陌生人打开聊天")
    await handler.handle_open_message(stranger_open)
    print()
    
    # 场景3：群成员打开群聊
    group_member_open = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123461,
        msg_type=MessageType.打开,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_group_member_003",
        to_user_name="wxid_bot123",
        content="群成员打开群聊",
        push_content="",
        msg_source="group",
        ver=3
    )
    group_member_open.group_id = "test_group@chatroom"
    
    print("场景3：群成员打开群聊")
    await handler.handle_open_message(group_member_open)
    print()


async def test_message_type_verification():
    """验证消息类型枚举"""
    print("=== 验证消息类型枚举 ===")
    
    print(f"MessageType.打开 的值: {MessageType.打开}")
    print(f"MessageType.打开 == 51: {MessageType.打开 == 51}")
    
    # 验证所有消息类型
    print("\n所有消息类型:")
    for msg_type in MessageType:
        print(f"  {msg_type.name} = {msg_type.value}")


async def main():
    """主测试函数"""
    print("开始测试打开消息（类型51）处理功能...\n")
    
    try:
        # 验证消息类型
        await test_message_type_verification()
        print("\n" + "="*50 + "\n")
        
        # 测试MessageHandler
        await test_open_message_handler()
        print("="*50 + "\n")
        
        # 测试MessageRouter
        await test_open_message_router()
        print("="*50 + "\n")
        
        # 测试不同场景
        await test_different_open_scenarios()
        
        print("测试完成！")
        print("\n注意：当收到类型为51的消息时，会自动打印对方的wxid信息。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
