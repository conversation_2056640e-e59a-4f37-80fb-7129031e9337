#!/usr/bin/env python3
"""
启动脚本
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import main

if __name__ == "__main__":
    # 确保日志目录存在
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # 检查.env文件是否存在
    env_file = project_root / ".env"
    if not env_file.exists():
        print("Warning: .env file not found. Please copy .env.example to .env and configure it.")
        print("Example: cp .env.example .env")
        sys.exit(1)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
