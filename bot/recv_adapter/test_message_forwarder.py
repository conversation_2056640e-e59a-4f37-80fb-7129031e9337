#!/usr/bin/env python3
"""
测试消息转发器功能
"""
import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from framework.core.models import StandardMessage, MessageType
from handlers.text_handler import Text<PERSON>essageHandler, MessageForwarder


async def test_message_forwarder():
    """测试消息转发器"""
    print("=== 测试消息转发器 ===")
    
    # 创建消息转发器
    forwarder = MessageForwarder()
    await forwarder.start()
    
    try:
        # 测试OpenIM消息
        openim_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123456,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_test123",
            uuid="uuid-test-123",
            from_user_name="test_user@openim",
            to_user_name="wxid_test123",
            content="这是一条OpenIM测试消息",
            push_content="",
            msg_source="",
            ver=3
        )
        openim_message.group_id = "12345@chatroom"
        
        print("测试OpenIM消息转发...")
        result = await forwarder.forward_message(openim_message)
        print(f"OpenIM消息转发结果: {result}")
        
        # 测试群公告消息
        announcement_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123457,
            msg_type=MessageType.应用,  # 应用消息类型
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_test123",
            uuid="uuid-test-123",
            from_user_name="wxid_admin",
            to_user_name="wxid_test123",
            content="群公告：明天下午2点开会，请大家准时参加",
            push_content="",
            msg_source="群公告",
            ver=3
        )
        announcement_message.group_id = "12345@chatroom"
        
        print("测试群公告消息转发...")
        result = await forwarder.forward_message(announcement_message)
        print(f"群公告消息转发结果: {result}")
        
    finally:
        await forwarder.stop()


async def test_text_handler():
    """测试文本处理器的转发功能"""
    print("\n=== 测试文本处理器 ===")
    
    handler = TextMessageHandler()
    await handler.start()
    
    try:
        # 测试OpenIM消息处理
        openim_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123458,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_test123",
            uuid="uuid-test-123",
            from_user_name="test_user@openim",
            to_user_name="wxid_test123",
            content="这是一条OpenIM消息，应该被转发",
            push_content="",
            msg_source="",
            ver=3
        )
        openim_message.group_id = "12345@chatroom"
        
        print("测试OpenIM消息处理...")
        result = await handler.handle_group_text(openim_message)
        print(f"OpenIM消息处理结果: {result}")
        
        # 测试群公告消息处理
        announcement_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123459,
            msg_type=MessageType.应用,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_test123",
            uuid="uuid-test-123",
            from_user_name="wxid_admin",
            to_user_name="wxid_test123",
            content="群公告：重要通知，请查看",
            push_content="",
            msg_source="",
            ver=3
        )
        announcement_message.group_id = "12345@chatroom"
        
        print("测试群公告消息处理...")
        # 先检查是否识别为群公告
        is_announcement = await handler.is_group_announcement(announcement_message)
        print(f"是否识别为群公告: {is_announcement}")
        
        if is_announcement:
            result = await handler.handle_group_announcement(announcement_message)
            print(f"群公告消息处理结果: {result}")
        
        # 测试普通群消息（不应该被转发）
        normal_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123460,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_test123",
            uuid="uuid-test-123",
            from_user_name="wxid_normal_user",
            to_user_name="wxid_test123",
            content="这是一条普通群消息",
            push_content="",
            msg_source="",
            ver=3
        )
        normal_message.group_id = "12345@chatroom"
        
        print("测试普通群消息处理...")
        result = await handler.handle_group_text(normal_message)
        print(f"普通群消息处理结果: {result}")
        
    finally:
        await handler.stop()


async def test_message_format_conversion():
    """测试消息格式转换"""
    print("\n=== 测试消息格式转换 ===")
    
    forwarder = MessageForwarder()
    
    # 创建测试消息
    test_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123461,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_test123",
        uuid="uuid-test-123",
        from_user_name="test_group@chatroom",
        to_user_name="wxid_test123",
        content="测试消息内容",
        push_content="",
        msg_source="",
        ver=3
    )
    
    # 转换格式
    converted = forwarder.convert_message_format(test_message)
    
    print("转换后的消息格式:")
    import json
    print(json.dumps(converted, indent=2, ensure_ascii=False))


async def main():
    """主测试函数"""
    print("开始测试消息转发器功能...\n")
    
    try:
        # 测试消息格式转换
        await test_message_format_conversion()
        
        # 测试消息转发器（注意：这会实际发送HTTP请求）
        print("\n注意：以下测试会向实际的HTTP接口发送请求")
        user_input = input("是否继续测试实际的HTTP转发？(y/N): ")
        
        if user_input.lower() == 'y':
            await test_message_forwarder()
            await test_text_handler()
        else:
            print("跳过HTTP转发测试")
        
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
