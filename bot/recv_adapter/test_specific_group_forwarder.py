#!/usr/bin/env python3
"""
测试特定群组消息转发功能
"""
import asyncio
import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from framework.core.models import StandardMessage, MessageType
from handlers.text_handler import TextMessageHandler, MessageForwarder


async def test_specific_group_format_conversion():
    """测试特定群组消息格式转换"""
    print("=== 测试特定群组消息格式转换 ===")
    
    forwarder = MessageForwarder()
    
    # 创建特定群组消息
    specific_group_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123456,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_group_member",
        to_user_name="wxid_bot123",
        content="这是来自特定群组的消息",
        push_content="",
        msg_source="",
        ver=3
    )
    specific_group_message.group_id = "47988506217@chatroom"
    
    # 转换为特定群组格式
    converted = forwarder.convert_specific_group_format(specific_group_message)
    
    print("特定群组消息转换后的格式:")
    print(json.dumps(converted, indent=2, ensure_ascii=False))
    
    # 验证格式是否正确
    print("\n格式验证:")
    print(f"TypeName: {converted['TypeName']}")
    print(f"Appid: {converted['Appid']}")
    print(f"Wxid: {converted['Wxid']}")
    print(f"FromUserName: {converted['Data']['FromUserName']['string']}")
    print(f"ToUserName: {converted['Data']['ToUserName']['string']}")
    print(f"Content: {converted['Data']['Content']['string']}")
    
    return converted


async def test_group_message_identification():
    """测试群组消息识别"""
    print("\n=== 测试群组消息识别 ===")
    
    handler = TextMessageHandler()
    await handler.start()
    
    try:
        # 测试特定群组消息
        specific_group_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123457,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_bot123",
            uuid="uuid-test-123",
            from_user_name="wxid_member1",
            to_user_name="wxid_bot123",
            content="特定群组消息测试",
            push_content="",
            msg_source="",
            ver=3
        )
        specific_group_message.group_id = "47988506217@chatroom"
        
        is_specific = await handler.is_specific_group_message(specific_group_message)
        print(f"特定群组消息识别结果: {is_specific}")
        
        # 测试其他群组消息
        other_group_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123458,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_bot123",
            uuid="uuid-test-123",
            from_user_name="wxid_member2",
            to_user_name="wxid_bot123",
            content="其他群组消息测试",
            push_content="",
            msg_source="",
            ver=3
        )
        other_group_message.group_id = "other_group@chatroom"
        
        is_other = await handler.is_specific_group_message(other_group_message)
        print(f"其他群组消息识别结果: {is_other}")
        
    finally:
        await handler.stop()


async def test_message_forwarding_logic():
    """测试消息转发逻辑"""
    print("\n=== 测试消息转发逻辑 ===")
    
    forwarder = MessageForwarder()
    await forwarder.start()
    
    try:
        # 测试特定群组消息转发格式选择
        specific_group_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123459,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_bot123",
            uuid="uuid-test-123",
            from_user_name="wxid_sender",
            to_user_name="wxid_bot123",
            content="测试特定群组格式选择",
            push_content="",
            msg_source="",
            ver=3
        )
        specific_group_message.group_id = "47988506217@chatroom"
        
        print("测试特定群组消息转发格式选择...")
        print("注意：这会实际发送HTTP请求")
        
        # 这里只测试格式转换，不实际发送
        if hasattr(specific_group_message, 'group_id') and specific_group_message.group_id == "47988506217@chatroom":
            converted_data = forwarder.convert_specific_group_format(specific_group_message)
            print("使用特定群组格式")
        else:
            converted_data = forwarder.convert_message_format(specific_group_message)
            print("使用默认格式")
        
        print("转换后的数据:")
        print(json.dumps(converted_data, indent=2, ensure_ascii=False))
        
    finally:
        await forwarder.stop()


async def test_text_handler_integration():
    """测试文本处理器集成"""
    print("\n=== 测试文本处理器集成 ===")
    
    handler = TextMessageHandler()
    await handler.start()
    
    try:
        # 测试特定群组消息处理
        specific_group_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123460,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_bot123",
            uuid="uuid-test-123",
            from_user_name="wxid_test_user",
            to_user_name="wxid_bot123",
            content="集成测试：特定群组消息",
            push_content="",
            msg_source="",
            ver=3
        )
        specific_group_message.group_id = "47988506217@chatroom"
        
        print("测试特定群组消息处理...")
        result = await handler.handle_specific_group_message(specific_group_message)
        print(f"特定群组消息处理结果: {result}")
        
        # 测试普通群组消息（不应该被转发）
        normal_group_message = StandardMessage(
            msg_name="AddMsgs",
            msg_id=123461,
            msg_type=MessageType.文本,
            timestamp=1705043418,
            is_self_message=False,
            wxid="wxid_bot123",
            uuid="uuid-test-123",
            from_user_name="wxid_normal_user",
            to_user_name="wxid_bot123",
            content="普通群组消息",
            push_content="",
            msg_source="",
            ver=3
        )
        normal_group_message.group_id = "normal_group@chatroom"
        
        print("测试普通群组消息处理...")
        is_specific = await handler.is_specific_group_message(normal_group_message)
        print(f"普通群组消息是否被识别为特定群组: {is_specific}")
        
    finally:
        await handler.stop()


async def test_format_comparison():
    """对比两种格式的差异"""
    print("\n=== 对比两种格式的差异 ===")
    
    forwarder = MessageForwarder()
    
    # 创建测试消息
    test_message = StandardMessage(
        msg_name="AddMsgs",
        msg_id=123462,
        msg_type=MessageType.文本,
        timestamp=1705043418,
        is_self_message=False,
        wxid="wxid_bot123",
        uuid="uuid-test-123",
        from_user_name="wxid_test_sender",
        to_user_name="wxid_bot123",
        content="格式对比测试消息",
        push_content="",
        msg_source="",
        ver=3
    )
    
    # 默认格式
    default_format = forwarder.convert_message_format(test_message)
    print("默认格式:")
    print(json.dumps(default_format, indent=2, ensure_ascii=False))
    
    print("\n" + "="*50)
    
    # 特定群组格式
    specific_format = forwarder.convert_specific_group_format(test_message)
    print("特定群组格式:")
    print(json.dumps(specific_format, indent=2, ensure_ascii=False))
    
    print("\n主要差异:")
    print(f"Wxid: {default_format['Wxid']} vs {specific_format['Wxid']}")
    print(f"FromUserName: {default_format['Data']['FromUserName']['string']} vs {specific_format['Data']['FromUserName']['string']}")
    print(f"ToUserName: {default_format['Data']['ToUserName']['string']} vs {specific_format['Data']['ToUserName']['string']}")


async def main():
    """主测试函数"""
    print("开始测试特定群组消息转发功能...\n")
    
    try:
        # 测试格式转换
        await test_specific_group_format_conversion()
        
        # 测试消息识别
        await test_group_message_identification()
        
        # 测试转发逻辑
        await test_message_forwarding_logic()
        
        # 测试集成功能
        print("\n注意：以下测试可能会发送实际的HTTP请求")
        user_input = input("是否继续测试集成功能？(y/N): ")
        
        if user_input.lower() == 'y':
            await test_text_handler_integration()
        else:
            print("跳过集成测试")
        
        # 格式对比
        await test_format_comparison()
        
        print("\n测试完成！")
        print("\n功能说明:")
        print("- 群组 47988506217@chatroom 的消息会使用特定格式转发")
        print("- Wxid 使用 'YBA-19990312'")
        print("- FromUserName 和 ToUserName 都使用 '47672310999@chatroom'")
        print("- 其他群组和OpenIM消息仍使用原有格式")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
