"""
消息路由器 - 统一管理消息分发到不同的处理器
"""
from typing import Dict, Any, Optional
from loguru import logger

from framework.core.models import StandardMessage, MessageType
from .message_handler import MessageHandler
from .text_handler import TextMessageHandler
from .group_handler import GroupMessageHandler
from .config import HandlerConfig


class MessageRouter:
    """消息路由器"""
    
    def __init__(self):
        # 初始化各种处理器
        self.message_handler = MessageHandler()
        self.text_handler = TextMessageHandler()
        self.group_handler = GroupMessageHandler()
        
        # 加载配置
        self.config = HandlerConfig()
        
        # 消息类型路由映射
        self.type_routes = {
            MessageType.文本: self._route_text_message,
            MessageType.图片: self._route_media_message,
            MessageType.语音: self._route_media_message,
            MessageType.视频: self._route_media_message,
            MessageType.表情: self._route_emoji_message,
            MessageType.应用: self._route_app_message,
            MessageType.文件: self._route_file_message,
            MessageType.名片: self._route_card_message,
            MessageType.位置: self._route_location_message,
            MessageType.打开: self._route_open_message,
        }
    
    async def route_message(self, message: StandardMessage) -> bool:
        """
        路由消息到合适的处理器
        
        Args:
            message: 标准化后的消息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            
            # 根据消息类型路由
            if message.msg_type in self.type_routes:
                route_func = self.type_routes[message.msg_type]
                return await route_func(message)
            else:
                # 未知消息类型，使用默认处理器
                logger.debug(f"Unknown message type {message.msg_type}, using default handler")
                return await self.message_handler.handle_message(message)
                
        except Exception as e:
            logger.error(f"Error routing message {message.msg_id}: {e}")
            return False
    
    async def _route_text_message(self, message: StandardMessage) -> bool:
        """路由文本消息"""
        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            # 群组文本消息
            if self.config.get_group_config().get('enabled', True):
                return await self.group_handler.handle_group_message(message)
            else:
                logger.debug(f"Group handler disabled for message {message.msg_id}")
                return True
        else:
            # 私聊文本消息
            if self.config.get_text_config().get('enabled', True):
                return await self.text_handler.handle_text_message(message)
            else:
                logger.debug(f"Text handler disabled for message {message.msg_id}")
                return True
    
    async def _route_media_message(self, message: StandardMessage) -> bool:
        """路由媒体消息（图片、语音、视频）"""
        # 媒体消息也可能需要群组处理
        if hasattr(message, 'group_id') and message.group_id:
            # 群组媒体消息
            return await self.group_handler.handle_group_message(message)
        else:
            # 私聊媒体消息
            return await self.message_handler.handle_message(message)
    
    async def _route_emoji_message(self, message: StandardMessage) -> bool:
        """路由表情消息"""
        # 表情消息可能包含文本内容，需要特殊处理
        if hasattr(message, 'group_id') and message.group_id:
            return await self.group_handler.handle_group_message(message)
        else:
            return await self.message_handler.handle_message(message)
    
    async def _route_app_message(self, message: StandardMessage) -> bool:
        """路由应用消息（链接、小程序等）"""
        # 应用消息可能需要特殊处理
        if hasattr(message, 'group_id') and message.group_id:
            return await self.group_handler.handle_group_message(message)
        else:
            return await self.message_handler.handle_message(message)
    
    async def _route_file_message(self, message: StandardMessage) -> bool:
        """路由文件消息"""
        if hasattr(message, 'group_id') and message.group_id:
            return await self.group_handler.handle_group_message(message)
        else:
            return await self.message_handler.handle_message(message)
    
    async def _route_card_message(self, message: StandardMessage) -> bool:
        """路由名片消息"""
        if hasattr(message, 'group_id') and message.group_id:
            return await self.group_handler.handle_group_message(message)
        else:
            return await self.message_handler.handle_message(message)
    
    async def _route_location_message(self, message: StandardMessage) -> bool:
        """路由位置消息"""
        if hasattr(message, 'group_id') and message.group_id:
            return await self.group_handler.handle_group_message(message)
        else:
            return await self.message_handler.handle_message(message)

    async def _route_open_message(self, message: StandardMessage) -> bool:
        """路由打开消息（消息类型51）"""
        # 打开消息直接使用message_handler处理，专门打印对方wxid
        return await self.message_handler.handle_message(message)
    
    def get_handler_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            'message_handler': 'active',
            'text_handler': 'active',
            'group_handler': 'active',
            'config_loaded': True,
            'supported_types': list(self.type_routes.keys()),
        }
    
    def reload_config(self):
        """重新加载配置"""
        try:
            # 重新初始化配置
            self.config = HandlerConfig()
            logger.info("Handler configuration reloaded")
        except Exception as e:
            logger.error(f"Failed to reload handler configuration: {e}")
    
    async def handle_special_message(self, message: StandardMessage, msg_name: str) -> bool:
        """处理特殊消息类型（如联系人变更、掉线等）"""
        logger.info(f"Handling special message: {msg_name}")
        
        if msg_name == "ModContacts":
            return await self._handle_contact_change(message)
        elif msg_name == "DelContacts":
            return await self._handle_contact_delete(message)
        elif msg_name == "Offline":
            return await self._handle_offline(message)
        else:
            logger.debug(f"Unknown special message type: {msg_name}")
            return True
    
    async def _handle_contact_change(self, message: StandardMessage) -> bool:
        """处理联系人变更"""
        logger.info(f"Contact changed: {message.content}")
        # TODO: 实现联系人变更处理逻辑
        return True
    
    async def _handle_contact_delete(self, message: StandardMessage) -> bool:
        """处理联系人删除"""
        logger.info(f"Contact deleted: {message.content}")
        # TODO: 实现联系人删除处理逻辑
        return True
    
    async def _handle_offline(self, message: StandardMessage) -> bool:
        """处理掉线通知"""
        logger.warning(f"Account offline: {message.content}")
        # TODO: 实现掉线处理逻辑
        return True
