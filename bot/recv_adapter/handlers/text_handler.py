"""
文本消息处理器 - 专门处理文本消息的各种场景
"""
import re
import random
import time
import aiohttp
from typing import Dict, Any, List, Optional
from loguru import logger

from framework.core.models import StandardMessage


class MessageForwarder:
    """消息转发器 - 将消息转发到指定的HTTP接口"""

    def __init__(self, target_url: str = "http://cloud.yaoboan.com:2533/api/receive_message"):
        self.target_url = target_url
        self.session = None

    async def start(self):
        """启动转发器"""
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def stop(self):
        """停止转发器"""
        if self.session:
            await self.session.close()
            self.session = None

    def convert_message_format(self, message: StandardMessage) -> Dict[str, Any]:
        """将StandardMessage转换为目标格式"""
        # 生成随机消息ID
        random_msg_id = random.randint(100000, 999999)

        # 获取当前时间戳
        current_time = int(time.time())

        # 构建目标格式
        converted_message = {
            "TypeName": "AddMsg",
            "Appid": "wx_K0G1TkCSGAjEd1nLdPr4n",
            "Wxid": "wxid_phyyedw9xap22",
            "Data": {
                "MsgId": random_msg_id,
                "FromUserName": {
                    "string": message.group_id
                },
                "ToUserName": {
                    "string": "wxid_0xsqb3o0tsvz22"
                },
                "MsgType": 1,  # 文本消息
                "Content": {
                    "string": message.content
                },
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {
                    "iLen": 0
                },
                "CreateTime": current_time
            }
        }

        return converted_message

    def convert_specific_group_format(self, message: StandardMessage) -> Dict[str, Any]:
        """将特定群组消息转换为指定格式"""
        # 生成随机消息ID
        random_msg_id = random.randint(100000, 999999)

        # 使用消息的原始时间戳
        create_time = message.timestamp

        # 构建特定群组的目标格式
        converted_message = {
            "TypeName": "AddMsg",
            "Appid": "wx_K0G1TkCSGAjEd1nLdPr4n",
            "Wxid": "YBA-19990312",
            "Data": {
                "MsgId": random_msg_id,
                "FromUserName": {
                    "string": "47672310999@chatroom"
                },
                "ToUserName": {
                    "string": "47672310999@chatroom"
                },
                "MsgType": 1,  # 文本消息
                "Content": {
                    "string": message.content
                },
                "Status": 3,
                "ImgStatus": 1,
                "ImgBuf": {
                    "iLen": 0
                },
                "CreateTime": create_time
            }
        }

        return converted_message

    async def forward_message(self, message: StandardMessage) -> bool:
        """转发消息到目标接口"""
        if not self.session:
            await self.start()

        try:
            # 检查是否是特定群组消息
            if hasattr(message, 'group_id') and message.group_id == "47988506217@chatroom":
                # 使用特定群组格式
                converted_data = self.convert_specific_group_format(message)
                logger.info(f"Using specific group format for group {message.group_id}")
            else:
                # 使用默认格式
                converted_data = self.convert_message_format(message)

            # 发送HTTP请求
            async with self.session.post(
                self.target_url,
                json=converted_data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                if response.status == 200:
                    logger.info(f"Successfully forwarded message {message.msg_id} to {self.target_url}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"Failed to forward message {message.msg_id}: "
                               f"status={response.status}, response={response_text}")
                    return False

        except Exception as e:
            logger.error(f"Error forwarding message {message.msg_id}: {e}")
            return False


class TextMessageHandler:
    """文本消息处理器"""

    def __init__(self):
        # 命令处理器映射
        self.command_handlers = {
            '/help': self.handle_help_command,
            '/status': self.handle_status_command,
            '/ping': self.handle_ping_command,
        }

        # 关键词处理器
        self.keyword_handlers = {
            '你好': self.handle_greeting,
            'hello': self.handle_greeting,
            'hi': self.handle_greeting,
        }

        # 初始化消息转发器
        self.message_forwarder = MessageForwarder()

    async def start(self):
        """启动处理器"""
        await self.message_forwarder.start()

    async def stop(self):
        """停止处理器"""
        await self.message_forwarder.stop()
    
    async def handle_text_message(self, message: StandardMessage) -> bool:
        """
        处理文本消息
        
        Args:
            message: 标准化后的消息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            content = message.content.strip()
            
            # 检查是否是命令
            if content.startswith('/'):
                return await self.handle_command(message, content)
            
            # 检查关键词
            for keyword, handler in self.keyword_handlers.items():
                if keyword.lower() in content.lower():
                    return await handler(message)
            
            # 检查是否是群消息
            if hasattr(message, 'group_id') and message.group_id:
                return await self.handle_group_text(message)
            else:
                return await self.handle_private_text(message)
                
        except Exception as e:
            logger.error(f"Error handling text message {message.msg_id}: {e}")
            return False
    
    async def handle_command(self, message: StandardMessage, content: str) -> bool:
        """处理命令消息"""
        command = content.split()[0].lower()
        
        if command in self.command_handlers:
            handler = self.command_handlers[command]
            return await handler(message, content)
        else:
            logger.debug(f"Unknown command: {command}")
            return await self.handle_unknown_command(message, command)
    
    async def handle_help_command(self, message: StandardMessage, content: str) -> bool:
        """处理帮助命令"""
        logger.info(f"Help command from {message.from_user_name}")
        
        help_text = """
🤖 机器人帮助信息

可用命令：
/help - 显示帮助信息
/status - 查看机器人状态
/ping - 测试连接

支持的功能：
• 文本消息处理
• 图片识别
• 语音转文字
• 群组管理
        """
        
        # TODO: 发送帮助信息回复
        logger.info(f"Would send help text to {message.from_user_name}")
        return True
    
    async def handle_status_command(self, message: StandardMessage, content: str) -> bool:
        """处理状态命令"""
        logger.info(f"Status command from {message.from_user_name}")
        
        # TODO: 获取机器人状态信息并回复
        status_text = "🟢 机器人运行正常"
        logger.info(f"Would send status to {message.from_user_name}: {status_text}")
        return True
    
    async def handle_ping_command(self, message: StandardMessage, content: str) -> bool:
        """处理ping命令"""
        logger.info(f"Ping command from {message.from_user_name}")
        
        # TODO: 发送pong回复
        logger.info(f"Would send pong to {message.from_user_name}")
        return True
    
    async def handle_unknown_command(self, message: StandardMessage, command: str) -> bool:
        """处理未知命令"""
        logger.info(f"Unknown command '{command}' from {message.from_user_name}")
        
        # TODO: 发送未知命令提示
        logger.info(f"Would send unknown command message to {message.from_user_name}")
        return True
    
    async def handle_greeting(self, message: StandardMessage) -> bool:
        """处理问候消息"""
        logger.info(f"Greeting from {message.from_user_name}")
        
        # TODO: 发送问候回复
        logger.info(f"Would send greeting reply to {message.from_user_name}")
        return True
    
    async def handle_group_text(self, message: StandardMessage) -> bool:
        """处理群组文本消息"""
        logger.info(f"Group text message in {message.group_id} from {message.from_user_name}")
        
        # 检查是否被@
        if hasattr(message, 'at_list') and message.at_list:
            if message.wxid in message.at_list:
                return await self.handle_group_mention(message)
        
        # 检查是否是OpenIM消息
        if message.from_user_name.endswith("@openim"):
            return await self.handle_openim_message(message)

        # 检查是否是群公告消息
        if await self.is_group_announcement(message):
            return await self.handle_group_announcement(message)

        # 检查是否是特定群组消息需要转发
        if await self.is_specific_group_message(message):
            return await self.handle_specific_group_message(message)

        # 普通群消息
        return await self.handle_normal_group_message(message)
    
    async def handle_private_text(self, message: StandardMessage) -> bool:
        """处理私聊文本消息"""
        logger.info(f"Private text message from {message.from_user_name}")
        
        # TODO: 实现私聊消息处理逻辑
        return True
    
    async def handle_group_mention(self, message: StandardMessage) -> bool:
        """处理群组@消息"""
        logger.info(f"Bot mentioned in group {message.group_id} by {message.from_user_name}")
        
        content = message.content
        
        # 移除@信息，获取纯文本
        clean_content = self.clean_at_content(content)
        
        # TODO: 处理@消息的具体逻辑
        logger.info(f"Processing mention with content: {clean_content}")
        return True
    
    async def handle_openim_message(self, message: StandardMessage) -> bool:
        """处理OpenIM消息"""
        logger.info(f"tOpenIM message from {message.from_user_name}: {message.content}")

        # 转发OpenIM消息到指定接口
        try:
            success = await self.message_forwarder.forward_message(message)
            if success:
                logger.info(f"Successfully forwarded OpenIM message {message.msg_id}")
            else:
                logger.error(f"Failed to forward OpenIM message {message.msg_id}")
            return success
        except Exception as e:
            logger.error(f"Error handling OpenIM message {message.msg_id}: {e}")
            return False

    async def is_group_announcement(self, message: StandardMessage) -> bool:
        """判断是否是群公告消息"""
        # 群公告消息通常是应用消息类型(49)，子类型为群公告(87)
        # 或者内容包含群公告相关的关键词
        if message.msg_type == 49:  # 应用消息
            # 检查消息源或内容是否包含群公告标识
            if "群公告" in message.content or "群通知" in message.content:
                return True
            # 检查消息源字段
            if hasattr(message, 'msg_source') and "群公告" in message.msg_source:
                return True

        # 检查内容是否包含群公告关键词
        announcement_keywords = ["群公告", "群通知", "管理员发布", "群主发布"]
        for keyword in announcement_keywords:
            if keyword in message.content:
                return True

        return False

    async def handle_group_announcement(self, message: StandardMessage) -> bool:
        """处理群公告消息"""
        logger.info(f"Group announcement from {message.from_user_name} in {message.group_id}: {message.content}")

        # 转发群公告消息到指定接口
        try:
            success = await self.message_forwarder.forward_message(message)
            if success:
                logger.info(f"Successfully forwarded group announcement {message.msg_id}")
            else:
                logger.error(f"Failed to forward group announcement {message.msg_id}")
            return success
        except Exception as e:
            logger.error(f"Error handling group announcement {message.msg_id}: {e}")
            return False

    async def is_specific_group_message(self, message: StandardMessage) -> bool:
        """判断是否是需要转发的特定群组消息"""
        # 检查是否是指定的群组
        if hasattr(message, 'group_id') and message.group_id == "47988506217@chatroom":
            return True
        return False

    async def handle_specific_group_message(self, message: StandardMessage) -> bool:
        """处理特定群组消息"""
        logger.info(f"Specific group message from {message.from_user_name} in {message.group_id}: {message.content}")

        # 转发特定群组消息到指定接口
        try:
            success = await self.message_forwarder.forward_message(message)
            if success:
                logger.info(f"Successfully forwarded specific group message {message.msg_id}")
            else:
                logger.error(f"Failed to forward specific group message {message.msg_id}")
            return success
        except Exception as e:
            logger.error(f"Error handling specific group message {message.msg_id}: {e}")
            return False

    async def handle_normal_group_message(self, message: StandardMessage) -> bool:
        """处理普通群消息"""
        logger.debug(f"Normal group message in {message.group_id}: {message.content}")
        
        # TODO: 实现普通群消息处理逻辑
        # 可以根据群组配置决定是否响应
        return True
    
    def clean_at_content(self, content: str) -> str:
        """清理@信息，获取纯文本内容"""
        # 移除@信息的正则表达式
        # 格式通常是 @昵称 或 @wxid
        cleaned = re.sub(r'@[^\s]+\s*', '', content)
        return cleaned.strip()
    
    def extract_mentions(self, content: str) -> List[str]:
        """提取消息中的@用户列表"""
        # 提取@信息
        mentions = re.findall(r'@([^\s]+)', content)
        return mentions
    
    def is_question(self, content: str) -> bool:
        """判断是否是问题"""
        question_indicators = ['?', '？', '吗', '呢', '如何', '怎么', '什么', '为什么']
        return any(indicator in content for indicator in question_indicators)
    
    def contains_keywords(self, content: str, keywords: List[str]) -> bool:
        """检查内容是否包含关键词"""
        content_lower = content.lower()
        return any(keyword.lower() in content_lower for keyword in keywords)
