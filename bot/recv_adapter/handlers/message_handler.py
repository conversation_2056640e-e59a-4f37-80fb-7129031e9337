"""
消息处理器 - 处理不同类型的消息
"""
from typing import Dict, Any, Optional
from loguru import logger

from framework.core.models import StandardMessage, MessageType


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self):
        self.handlers = {
            MessageType.文本: self.handle_text_message,
            MessageType.图片: self.handle_image_message,
            MessageType.语音: self.handle_voice_message,
            MessageType.视频: self.handle_video_message,
            MessageType.表情: self.handle_emoji_message,
            MessageType.应用: self.handle_app_message,
            MessageType.文件: self.handle_file_message,
            MessageType.名片: self.handle_card_message,
            MessageType.位置: self.handle_location_message,
            MessageType.打开: self.handle_open_message,
        }
    
    async def handle_message(self, message: StandardMessage) -> bool:
        """
        处理消息的主入口
        
        Args:
            message: 标准化后的消息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            # 检查是否是支持的消息类型
            if message.msg_type not in self.handlers:
                logger.debug(f"Unsupported message type: {message.msg_type}")
                return True
            
            # 调用对应的处理器
            handler = self.handlers[message.msg_type]
            return await handler(message)
            
        except Exception as e:
            logger.error(f"Error handling message {message.msg_id}: {e}")
            return False
    
    async def handle_text_message(self, message: StandardMessage) -> bool:
        """处理文本消息"""
        logger.info(f"Processing text message: {message.msg_id}")
        
        # 检查是否是群消息
        if hasattr(message, 'group_id') and message.group_id:
            return await self.handle_group_text_message(message)
        else:
            return await self.handle_private_text_message(message)
    
    async def handle_group_text_message(self, message: StandardMessage) -> bool:
        """处理群组文本消息"""
        logger.info(f"Processing group text message from {message.from_user_name} in {message.group_id}")
        
        # 检查是否是OpenIM消息
        if message.from_user_name.endswith("@openim"):
            return await self.handle_openim_message(message)
        
        # 检查是否被@
        if hasattr(message, 'at_list') and message.at_list:
            if message.wxid in message.at_list:
                logger.info(f"Bot was mentioned in group {message.group_id}")
                return await self.handle_mention_message(message)
        
        # 普通群消息处理
        return await self.process_group_message(message)
    
    async def handle_private_text_message(self, message: StandardMessage) -> bool:
        """处理私聊文本消息"""
        logger.info(f"Processing private text message from {message.from_user_name}")
        return await self.process_private_message(message)
    
    async def handle_openim_message(self, message: StandardMessage) -> bool:
        """处理OpenIM消息"""
        logger.info(f"Processing OpenIM message: {message.content}")
        # TODO: 实现OpenIM消息处理逻辑
        return True
    
    async def handle_mention_message(self, message: StandardMessage) -> bool:
        """处理@机器人的消息"""
        logger.info(f"Processing mention message: {message.content}")
        # TODO: 实现@消息处理逻辑
        return True
    
    async def process_group_message(self, message: StandardMessage) -> bool:
        """处理普通群消息"""
        logger.debug(f"Processing group message: {message.content}")
        # TODO: 实现群消息处理逻辑
        return True
    
    async def process_private_message(self, message: StandardMessage) -> bool:
        """处理私聊消息"""
        logger.debug(f"Processing private message: {message.content}")
        # TODO: 实现私聊消息处理逻辑
        return True
    
    async def handle_image_message(self, message: StandardMessage) -> bool:
        """处理图片消息"""
        logger.info(f"Processing image message: {message.msg_id}")
        # TODO: 实现图片消息处理逻辑
        return True
    
    async def handle_voice_message(self, message: StandardMessage) -> bool:
        """处理语音消息"""
        logger.info(f"Processing voice message: {message.msg_id}")
        # TODO: 实现语音消息处理逻辑
        return True
    
    async def handle_video_message(self, message: StandardMessage) -> bool:
        """处理视频消息"""
        logger.info(f"Processing video message: {message.msg_id}")
        # TODO: 实现视频消息处理逻辑
        return True
    
    async def handle_emoji_message(self, message: StandardMessage) -> bool:
        """处理表情消息"""
        logger.info(f"Processing emoji message: {message.msg_id}")
        # TODO: 实现表情消息处理逻辑
        return True
    
    async def handle_app_message(self, message: StandardMessage) -> bool:
        """处理应用消息（链接、小程序等）"""
        logger.info(f"Processing app message: {message.msg_id}")
        # TODO: 实现应用消息处理逻辑
        return True
    
    async def handle_file_message(self, message: StandardMessage) -> bool:
        """处理文件消息"""
        logger.info(f"Processing file message: {message.msg_id}")
        # TODO: 实现文件消息处理逻辑
        return True
    
    async def handle_card_message(self, message: StandardMessage) -> bool:
        """处理名片消息"""
        logger.info(f"Processing card message: {message.msg_id}")
        # TODO: 实现名片消息处理逻辑
        return True
    
    async def handle_location_message(self, message: StandardMessage) -> bool:
        """处理位置消息"""
        logger.info(f"Processing location message: {message.msg_id}")
        # TODO: 实现位置消息处理逻辑
        return True

    async def handle_open_message(self, message: StandardMessage) -> bool:
        """处理打开消息（消息类型51）"""
        logger.info(f"=== 打开消息 (类型51) ===")
        logger.info(f"对方wxid: {message.from_user_name}")
        logger.info(f"消息ID: {message.msg_id}")
        logger.info(f"时间戳: {message.timestamp}")
        logger.info(f"内容: {message.content}")
        logger.info(f"消息源: {message.msg_source}")

        # 如果是群消息，也打印群ID
        if hasattr(message, 'group_id') and message.group_id:
            logger.info(f"群组ID: {message.group_id}")

        # 打印完整的消息信息用于调试
        logger.debug(f"完整消息信息: {message.model_dump()}")

        return True
