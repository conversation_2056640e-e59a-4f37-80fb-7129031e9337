"""
消息处理器配置
"""
from typing import Dict, Any, List


class HandlerConfig:
    """消息处理器配置类"""
    
    # 文本消息处理配置
    TEXT_HANDLER_CONFIG = {
        'enabled': True,
        'commands': {
            '/help': {'enabled': True, 'admin_only': False},
            '/status': {'enabled': True, 'admin_only': False},
            '/ping': {'enabled': True, 'admin_only': False},
        },
        'keywords': {
            '你好': {'enabled': True, 'response': '你好！我是机器人助手'},
            'hello': {'enabled': True, 'response': 'Hello! I am a bot assistant'},
            'hi': {'enabled': True, 'response': 'Hi there!'},
        },
        'auto_reply': {
            'enabled': True,
            'private_chat': True,
            'group_chat': False,  # 群聊中只响应@消息
        }
    }
    
    # 群组消息处理配置
    GROUP_HANDLER_CONFIG = {
        'enabled': True,
        'features': {
            'auto_reply': True,
            'keyword_response': True,
            'welcome_new_member': True,
            'anti_spam': True,
            'admin_commands': True,
        },
        'spam_detection': {
            'enabled': True,
            'keywords': ['广告', '推广', '加微信', '免费', '赚钱'],
            'repeat_threshold': 0.3,  # 重复字符阈值
        },
        'admin_commands': {
            '开启': {'enabled': True, 'admin_only': True},
            '关闭': {'enabled': True, 'admin_only': True},
            '状态': {'enabled': True, 'admin_only': True},
            '帮助': {'enabled': True, 'admin_only': False},
        }
    }
    
    # 通用消息处理配置
    MESSAGE_HANDLER_CONFIG = {
        'enabled': True,
        'supported_types': {
            'text': True,
            'image': True,
            'voice': True,
            'video': True,
            'emoji': True,
            'app': True,
            'file': True,
            'card': True,
            'location': True,
        },
        'processing': {
            'forward_unsupported': True,  # 转发不支持的消息类型
            'log_all_messages': True,     # 记录所有消息
        }
    }
    
    # 群组特定配置（可以为不同群组设置不同配置）
    GROUP_SPECIFIC_CONFIG = {
        # 示例群组配置
        'default': {
            'bot_enabled': True,
            'auto_reply': True,
            'keyword_response': True,
            'welcome_new_member': True,
            'anti_spam': True,
            'admin_only_commands': True,
        }
    }
    
    @classmethod
    def get_text_config(cls) -> Dict[str, Any]:
        """获取文本处理器配置"""
        return cls.TEXT_HANDLER_CONFIG.copy()
    
    @classmethod
    def get_group_config(cls, group_id: str = None) -> Dict[str, Any]:
        """获取群组处理器配置"""
        config = cls.GROUP_HANDLER_CONFIG.copy()
        
        # 如果有群组特定配置，则合并
        if group_id and group_id in cls.GROUP_SPECIFIC_CONFIG:
            group_specific = cls.GROUP_SPECIFIC_CONFIG[group_id]
            config['features'].update(group_specific)
        else:
            # 使用默认配置
            config['features'].update(cls.GROUP_SPECIFIC_CONFIG['default'])
        
        return config
    
    @classmethod
    def get_message_config(cls) -> Dict[str, Any]:
        """获取通用消息处理器配置"""
        return cls.MESSAGE_HANDLER_CONFIG.copy()
    
    @classmethod
    def is_command_enabled(cls, command: str) -> bool:
        """检查命令是否启用"""
        commands = cls.TEXT_HANDLER_CONFIG.get('commands', {})
        return commands.get(command, {}).get('enabled', False)
    
    @classmethod
    def is_keyword_enabled(cls, keyword: str) -> bool:
        """检查关键词是否启用"""
        keywords = cls.TEXT_HANDLER_CONFIG.get('keywords', {})
        return keywords.get(keyword, {}).get('enabled', False)
    
    @classmethod
    def get_keyword_response(cls, keyword: str) -> str:
        """获取关键词响应"""
        keywords = cls.TEXT_HANDLER_CONFIG.get('keywords', {})
        return keywords.get(keyword, {}).get('response', '')
    
    @classmethod
    def is_spam_keyword(cls, text: str) -> bool:
        """检查是否包含垃圾关键词"""
        spam_keywords = cls.GROUP_HANDLER_CONFIG.get('spam_detection', {}).get('keywords', [])
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in spam_keywords)
    
    @classmethod
    def get_repeat_threshold(cls) -> float:
        """获取重复字符阈值"""
        return cls.GROUP_HANDLER_CONFIG.get('spam_detection', {}).get('repeat_threshold', 0.3)
    
    @classmethod
    def update_group_config(cls, group_id: str, config: Dict[str, Any]):
        """更新群组配置"""
        if group_id not in cls.GROUP_SPECIFIC_CONFIG:
            cls.GROUP_SPECIFIC_CONFIG[group_id] = {}
        
        cls.GROUP_SPECIFIC_CONFIG[group_id].update(config)
    
    @classmethod
    def is_message_type_supported(cls, msg_type: str) -> bool:
        """检查消息类型是否支持"""
        supported_types = cls.MESSAGE_HANDLER_CONFIG.get('supported_types', {})
        return supported_types.get(msg_type, False)
