# 微信机器人消息接收处理程序

这是一个基于Python的微信机器人消息接收处理程序，用于替代n8n流程。程序监听RabbitMQ队列中的微信消息，进行处理后转发到指定的HTTP接口。

## 功能特性

- **多队列监听**: 同时监听多个RabbitMQ队列（wechatpadpro、wxapi、wx_msg）
- **Webhook接收**: 支持GeWe API的webhook消息接收（V3版本）
- **消息标准化**: 支持不同版本的消息格式，统一标准化处理
- **智能过滤**: 过滤历史消息和重复消息
- **群组处理**: 特殊处理群组消息，解析发送者、@列表等信息
- **消息分类**: 根据消息类型进行分类处理
- **HTTP转发**: 将处理后的消息转发到指定接口
- **定时清理**: 可选的RabbitMQ队列定时清理功能
- **异步处理**: 基于asyncio的高性能异步处理
- **并行接收**: RabbitMQ和Webhook接收器可同时运行

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制配置文件模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置相关参数：
```bash
# RabbitMQ连接配置
RABBITMQ_HOST=**************
RABBITMQ_USERNAME=your_username
RABBITMQ_PASSWORD=your_password

# HTTP转发配置
FORWARD_BASE_URL=http://**************:9312/recv

# Webhook配置
WEBHOOK_ENABLED=true
WEBHOOK_HOST=0.0.0.0
WEBHOOK_PORT=8080
WEBHOOK_PATH=/webhook

# 其他配置...
```

## 运行程序

```bash
python run.py
```

或者直接运行主程序：
```bash
python main.py
```

## 项目结构

```
bot/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── config.py               # 配置管理
├── models.py               # 数据模型定义
├── rabbitmq_client.py      # RabbitMQ客户端
├── message_processor.py    # 消息处理器
├── message_filter.py       # 消息过滤器
├── group_processor.py      # 群组消息处理器
├── http_client.py          # HTTP客户端
├── queue_cleaner.py        # 队列清理器
├── requirements.txt        # 依赖包列表
├── .env.example           # 配置文件模板
└── README.md              # 说明文档
```

## 消息处理流程

1. **监听队列**: 程序同时监听三个RabbitMQ队列
2. **原始过滤**: 对版本1.5和版本2的消息进行时间过滤
3. **消息标准化**: 将不同版本的消息格式统一标准化
4. **过滤去重**: 过滤历史消息和重复消息
5. **消息分类**: 根据消息类型和发送者进行分类
6. **群组处理**: 对群组消息进行特殊处理
7. **HTTP转发**: 将处理后的消息转发到指定接口

## 支持的消息版本

- **版本1**: wechatpadpro队列的消息格式
- **版本1.5**: wx_msg队列的消息格式
- **版本2**: wxapi队列的消息格式
- **版本3**: GeWe API webhook的消息格式

## Webhook接收器

程序内置了HTTP服务器来接收GeWe API的webhook消息：

- **接收地址**: `http://your-server:8080/webhook`
- **支持的消息类型**: AddMsg（普通消息）、ModContacts（联系人变更）、DelContacts（删除联系人）、Offline（掉线通知）
- **健康检查**: `GET http://your-server:8080/health`

### 配置GeWe API

在GeWe API中配置webhook地址：
```
http://your-server-ip:8080/webhook
```

## 日志

程序会在以下位置生成日志：
- 控制台输出：实时日志
- 文件日志：`logs/wechat_bot_receiver.log`（按天轮转，保留7天）

## 注意事项

1. 确保RabbitMQ服务正常运行且可访问
2. 确保HTTP转发目标服务正常运行
3. 根据实际环境调整配置参数
4. 建议在生产环境中使用进程管理工具（如systemd、supervisor等）
